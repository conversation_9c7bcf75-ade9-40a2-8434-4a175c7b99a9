{"name": "gold<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "dist/server.js", "type": "module", "scripts": {"start": "NODE_ENV=production node dist/server.js ", "dev": "npm run build && npx concurrently --kill-others \"npx firebase emulators:start\" \"node dist/server.js\"", "build": "npx tsc && cd frontend && npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/cookie-parser": "^1.4.7", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "concurrently": "^9.0.1", "firebase-tools": "^13.22.0", "typescript": "^5.6.2"}, "dependencies": {"@types/bcrypt": "^5.0.2", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "express": "^4.21.0", "firebase-admin": "^12.6.0", "jsonwebtoken": "^9.0.2"}}