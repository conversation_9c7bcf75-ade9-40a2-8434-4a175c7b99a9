import express, { Request, Response } from "express";
import cookieParser from "cookie-parser";
import { authMiddleware, JWT_SECRET } from "./auth.js";
import jwt from 'jsonwebtoken'

export const router = express.Router();

router.use(express.urlencoded());
router.use(cookieParser());


router.get("/user", authMiddleware, async (req: Request, res: Response) => {
    const token = req.cookies.token;
    const payload = jwt.verify(token, JWT_SECRET)

    res.status(200).json(payload);
})
