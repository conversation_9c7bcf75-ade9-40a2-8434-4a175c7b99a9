import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import MarketplaceClient from "./MarketplaceClient";

export default async function Marketplace() {
  const user = await getCurrentUser();

  if (!user) {
    redirect("/login");
  }

  return (
    <>
      <link rel="stylesheet" href="/css/marketplace.css" />
      <link rel="stylesheet" href="/css/component-modal.css" />
      <div className="wrapper">
        <Navigation />
        <MarketplaceClient />
        <Footer />
      </div>
    </>
  );
}
