"use client";

import { useRef } from "react";
import Image from "next/image";

export default function MarketplaceClient() {
  const gcdModalRef = useRef<HTMLDialogElement>(null);
  // Additional modal refs for future use
  // const igrowpodModalRef = useRef<HTMLDialogElement>(null);
  // const roicreModalRef = useRef<HTMLDialogElement>(null);
  // const thegiantModalRef = useRef<HTMLDialogElement>(null);
  // const wilsoninvestmentsModalRef = useRef<HTMLDialogElement>(null);
  // const livelabelsModalRef = useRef<HTMLDialogElement>(null);
  // const gtenterprisesModalRef = useRef<HTMLDialogElement>(null);
  // const msskModalRef = useRef<HTMLDialogElement>(null);
  // const goldModalRef = useRef<HTMLDialogElement>(null);

  const openModal = (modalRef: React.RefObject<HTMLDialogElement | null>) => {
    if (modalRef.current) {
      modalRef.current.style.display = "flex";
      modalRef.current.showModal();
    }
  };

  const closeModal = (modalRef: React.RefObject<HTMLDialogElement | null>) => {
    if (modalRef.current) {
      modalRef.current.close();
      modalRef.current.style.display = "none";
    }
  };

  const openDocument = (path: string) => {
    window.open(path);
  };

  return (
    <main>
      <div className="marketplace-cards">
        <div className="card-container">
          <Image src="/assets/images/marketplace/gcd.png" alt="GoldChain DAO" width={300} height={200} />
          <div className="info-container">
            <h2>GoldChain DAO</h2>
            <p>
              At GoldChain DAO, we are revolutionizing how assets are managed,
              tokenized, and traded. Our platform leverages blockchain technology
              to provide secure, transparent, and efficient solutions for asset
              management and investment opportunities.
            </p>
            <div className="buttons-container">
              <button
                className="button primary-button"
                onClick={() => openDocument("/documents/GoldChain_DAO_General_Presentation")}
              >
                Documents
              </button>
              <button
                className="button secondary-button"
                onClick={() => openModal(gcdModalRef)}
              >
                Schedule
              </button>
            </div>
          </div>
        </div>

        {/* Add more marketplace cards here following the same pattern */}
      </div>

      {/* Modals */}
      <dialog ref={gcdModalRef} className="modal">
        <div className="text-container">
          <h2>Schedule to Tokenize</h2>
          <p>
            Please contact <a href="mailto:<EMAIL>"><EMAIL></a> to schedule.
            <br/><br/> Automatic schedule feature is being added..
          </p>
        </div>
        <div className="button-container">
          <button onClick={() => closeModal(gcdModalRef)}>Close</button>
        </div>
      </dialog>

      {/* Add more modals following the same pattern */}
    </main>
  );
}
