import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

export default async function Dashboard() {
  const user = await getCurrentUser();

  if (!user) {
    redirect("/login");
  }

  return (
    <>
      <link rel="stylesheet" href="/css/dashboard.css" />
      <div className="wrapper">
        <Navigation />
        <main>
          <div className="dashboard-wrapper">
            <div className="dashboard-container">
              <div className="dashboard-title-container">
                <h1>Dashboard</h1>
                <p>Welcome to your GoldChain DAO dashboard.</p>
              </div>
              
              <div className="dashboard-content">
                <div className="user-info-section">
                  <h2>User Information</h2>
                  <div className="user-details">
                    <p><strong>Name:</strong> <span id="name">{user.firstName} {user.lastName}</span></p>
                    <p><strong>Email:</strong> <span id="email">{user.email}</span></p>
                    <p><strong>Phone:</strong> <span id="phone">{user.phone}</span></p>
                    <p><strong>Role:</strong> <span id="role">{user.role}</span></p>
                  </div>
                </div>

                <div className="dashboard-actions">
                  <div className="action-card">
                    <h3>Marketplace</h3>
                    <p>Explore tokenized assets and investment opportunities.</p>
                    <a href="/marketplace" className="button primary-button">
                      Visit Marketplace
                    </a>
                  </div>

                  <div className="action-card">
                    <h3>Portfolio</h3>
                    <p>View and manage your asset portfolio.</p>
                    <button className="button secondary-button" disabled>
                      Coming Soon
                    </button>
                  </div>

                  <div className="action-card">
                    <h3>Governance</h3>
                    <p>Participate in DAO governance and voting.</p>
                    <button className="button secondary-button" disabled>
                      Coming Soon
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
}
