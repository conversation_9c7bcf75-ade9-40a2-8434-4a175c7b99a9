import { NextRequest, NextResponse } from "next/server";
import { get, getId, verifyPassword } from "@/lib/user";
import { createToken } from "@/lib/auth";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    console.log("Login attempt for email:", email);

    if (!email || !password) {
      console.log("Missing email or password");
      return NextResponse.redirect(new URL("/login?error=missing_credentials", request.url));
    }

    const user = await get(email);
    console.log("User found:", user ? "Yes" : "No");

    if (!user) {
      console.log("User not found");
      return NextResponse.redirect(new URL("/login?error=user_not_found", request.url));
    }

    const passwordValid = await verifyPassword(email, password);
    console.log("Password valid:", passwordValid);

    if (!passwordValid) {
      console.log("Invalid password");
      return NextResponse.redirect(new URL("/login?error=invalid_password", request.url));
    }

    const userId = await getId(user.email);
    console.log("User ID:", userId);

    const token = createToken({
      sub: userId,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      role: user.role,
    });

    const response = NextResponse.redirect(new URL("/dashboard", request.url));
    response.cookies.set("token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 3600, // 1 hour
      path: "/",
    });

    console.log("Login successful, redirecting to dashboard");
    return response;
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.redirect(new URL("/login?error=server_error", request.url));
  }
}
