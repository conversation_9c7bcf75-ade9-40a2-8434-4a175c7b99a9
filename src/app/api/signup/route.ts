import { NextRequest, NextResponse } from "next/server";
import { create, get } from "@/lib/user";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const firstName = formData.get("firstName") as string;
    const lastName = formData.get("lastName") as string;
    const email = formData.get("email") as string;
    const phone = formData.get("phone") as string;
    const password = formData.get("password") as string;

    console.log("Signup attempt for:", { firstName, lastName, email, phone: phone ? "***" : "missing" });

    if (!firstName || !lastName || !email || !phone || !password) {
      console.log("Missing required fields");
      return NextResponse.redirect(new URL("/signup?error=missing_fields", request.url));
    }

    const existingUser = await get(email);
    if (existingUser) {
      console.log("User already exists");
      return NextResponse.redirect(new URL("/signup?error=user_exists", request.url));
    }

    await create(firstName, lastName, email, password, parseInt(phone));
    console.log("User created successfully");

    return NextResponse.redirect(new URL("/login?success=account_created", request.url));
  } catch (error) {
    console.error("Signup error:", error);
    return NextResponse.redirect(new URL("/signup?error=server_error", request.url));
  }
}
