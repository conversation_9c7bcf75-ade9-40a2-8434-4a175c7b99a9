import express from "express";
import path from "path";
import { authMiddleware } from "./auth.js";

export const router = express.Router();

router.get("/documents/:filename", authMiddleware, (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join(
    import.meta.dirname,
    "../",
    "frontend",
    "public",
    "documents",
    filename
  );
  res.sendFile(`${filePath + ".pdf"}`);
});
