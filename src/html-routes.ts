import express, { Request, Response } from "express";
import path from "path";
import { authMiddleware } from "./auth.js";

export const router = express.Router();

router.use('/assets',express.static(path.join(import.meta.dirname, '../frontend/public/assets/')));
router.use('/css',express.static(path.join(import.meta.dirname, '../frontend/public/css/')));
router.use('/js',express.static(path.join(import.meta.dirname, '../frontend/public/js/')));

const HTML_FILES_DIR = path.join(import.meta.dirname, '../frontend/public');

router.get("/", (req: Request, res: Response) => {
    
    res.sendFile('index.html', { root: HTML_FILES_DIR });
});

router.get("/about", (req: Request, res: Response) => {
    res.sendFile('about.html', { root: HTML_FILES_DIR });
});

router.get("/login", (req: Request, res: Response) => {
    res.sendFile('login.html', { root: HTML_FILES_DIR });
});

router.get("/signup", (req: Request, res: Response) => {
    res.sendFile('signup.html', { root: HTML_FILES_DIR });
});

/* PROTECTED ROUTES */

router.get("/dashboard", authMiddleware, (req: Request, res: Response) => {
    res.sendFile('dashboard.html', { root: HTML_FILES_DIR });
});

router.get("/marketplace", authMiddleware, (req: Request, res: Response) => {
    res.sendFile('marketplace.html', { root: HTML_FILES_DIR });
});
