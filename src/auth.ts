import express, { Request, Response } from "express";
import cookieParser from "cookie-parser";
import jwt from "jsonwebtoken";
import { create, get, getId, verifyPassword } from "./db/user.js";

export const router = express.Router();

router.use(express.urlencoded());
router.use(cookieParser());

export const JWT_SECRET = process.env.JWT_SECRET || "secret";

export const authMiddleware = (req: Request, res: Response, next: Function) => {
  try {
    jwt.verify(req.cookies.token, JWT_SECRET);
  } catch (VerifyErrors) {
    res.clearCookie("token");
    // res.status(401).json({ error: true, message: "Unauthorized." });
    // res.redirect(401, "/login"); // not working, need to add status code to redirect.
    res.redirect("/login");
    return;
  }

  next();
};

router.post("/signup", async (req: Request, res: Response) => {
  const { firstName, lastName, email, phone, password } = req.body;

  if (await get(email)) {
    // res.status(400).json({ error: true, message: "Invalid credentials." });
    // res.redirect(400, "/signup"); // not working, need to add status code to redirect.
    res.redirect("/signup");
    return;
  }

  await create(firstName, lastName, email, password, phone);

  // res.status(200).json({ message: "User created successfully." });
  // res.redirect(200, "/login"); // not working, need to add status code to redirect.
  res.redirect("/login");
});

router.post("/login", async (req: Request, res: Response) => {
  const { email, password }: { email: string; password: string } = req.body;

  const user = await get(email);

  if (!user || !(await verifyPassword(email, password))) {
    // res.status(400).json({ error: true, message: "Invalid credentials." });
    // res.redirect(400, "/login"); // not working, need to add status code to redirect.
    res.redirect("/login");
    return;
  }

  if (req.cookies.token) {
    res.clearCookie("token");
  }

  const userId = await getId(user.email);

  const token = jwt.sign({ sub: userId, firstName: user.firstName, lastName: user.lastName, email: user.email, phone: user.phone, role: user.role }, JWT_SECRET, {
    algorithm: "HS256",
    expiresIn: "1h",
  });
  res.cookie("token", token, { httpOnly: true, secure: true });

  // res.status(200).json({ message: "User signed in successfully." });
  // res.redirect(200, "/dashboard"); // not working, need to add status code to redirect.
  res.redirect("/dashboard");
});

router.post("/logout", (req: Request, res: Response) => {
  res.clearCookie("token");
  res.status(200).json({ message: "Logged out." });
  // res.redirect(200, "/"); // not working, need to add status code to redirect.
});

router.get("/logout", (req: Request, res: Response) => {
  res.clearCookie("token");
  // res.status(200).json({ message: "Logged out." });
  // res.redirect(200, "/"); // not working, need to add status code to redirect.
  res.redirect("/");
});
