import express from "express";
import { router as htmlRouter } from "./html-routes.js";
import { router as authRouter } from "./auth.js";
import { router as userRouter } from "./userapi.js";
import { router as documentRouter } from "./documents.js";

const app = express();
const PORT = process.env.PORT || 3005;

app.use(authRouter);
app.use(htmlRouter);
app.use(userRouter);
app.use(documentRouter);

app.listen(PORT, async () => {
  console.log(`Server is running on localhost:${PORT}`);
});
