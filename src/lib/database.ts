import { initializeApp, cert, getApps } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";

// Initialize Firebase Admin only if it hasn't been initialized
if (!getApps().length) {
  if (process.env.FIREBASE) {
    initializeApp({ credential: cert(JSON.parse(process.env.FIREBASE)) });
  } else {
    initializeApp({ projectId: "goldchaindao-4c6ce" });
  }
}

const db = getFirestore();

if (!process.env.FIREBASE) {
  try {
    db.settings({ host: "localhost", port: 8080, ssl: false });
  } catch (error) {
    // Firestore already initialized, ignore
    console.log("Firestore already initialized");
  }
}

export default db;
