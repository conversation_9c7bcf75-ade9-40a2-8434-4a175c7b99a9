import db from "./database.js";
import bcrypt from "bcrypt";

export type User = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  role: "admin" | "user";
};

export async function verifyPassword(email: string, password: string) {
  const hashedPassword = (await get(email)).password;
  return await bcrypt.compare(password, hashedPassword);
}

export async function get(email: string) {
  const snapshot = await db
    .collection("users")
    .where("email", "==", email)
    .get();

  const user = snapshot.docs[0]?.data();
  return user;
}


export async function getDocument(email: string) {
  const snapshot = await db
    .collection("users")
    .where("email", "==", email)
    .get();

  const document = snapshot.docs[0]
  return document;
}

export async function getId(email: string) {
  const userDocument = await getDocument(email)
  return userDocument.id
}

export async function create(
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  phone: number,
  role: string = "user"
) {
  const hashedPassword = await bcrypt.hash(password, 10);
  const docRef = db.collection("users").doc();
  const result = await docRef.set({
    firstName,
    lastName,
    email,
    password: hashedPassword,
    phone,
    role,
  });
}
