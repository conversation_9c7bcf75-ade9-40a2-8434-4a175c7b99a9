import db from "./database.js";
import bcrypt from "bcrypt";

export type User = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  role: "admin" | "user";
};

export async function verifyPassword(email: string, password: string): Promise<boolean> {
  const user = await get(email);
  if (!user) {
    return false;
  }
  return await bcrypt.compare(password, user.password);
}

export async function get(email: string): Promise<User | undefined> {
  const snapshot = await db
    .collection("users")
    .where("email", "==", email)
    .get();

  const user = snapshot.docs[0]?.data() as User | undefined;
  return user;
}

export async function getDocument(email: string): Promise<FirebaseFirestore.DocumentSnapshot | undefined> {
  const snapshot = await db
    .collection("users")
    .where("email", "==", email)
    .get();

  const document = snapshot.docs[0];
  return document;
}

export async function getId(email: string): Promise<string> {
  const userDocument = await getDocument(email);
  if (!userDocument) {
    throw new Error(`User with email ${email} not found`);
  }
  return userDocument.id;
}

export async function create(
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  phone: number,
  role: string = "user",
): Promise<void> {
  const hashedPassword = await bcrypt.hash(password, 10);
  const docRef = db.collection("users").doc();
  await docRef.set({
    firstName,
    lastName,
    email,
    password: hashedPassword,
    phone,
    role,
  });
}
