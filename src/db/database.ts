import { initializeApp, cert } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";

if (process.env.FIREBASE) {
  initializeApp({ credential: cert(JSON.parse(process.env.FIREBASE)) });
} else {
  initializeApp({ projectId: "goldchaindao-4c6ce" });
}
const db = getFirestore();

if (!process.env.FIREBASE) {
  db.settings({ host: "localhost", port: 8080, ssl: false });
}

export default db;
