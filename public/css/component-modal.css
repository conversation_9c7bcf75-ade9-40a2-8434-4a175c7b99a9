.modal {
    display: none;
    /* display: flex; */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: auto auto;
    gap: 80px;

    max-width: fit-content;
    max-height: fit-content;

    padding: 5% 10%;


    background-image: url("../assets/images/modal/modalbackground.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;

    font-family: "Lexend";
    text-align: center;

    border: 1px solid #262626;
    border-radius: 20px;

}

.modal .text-container {
    display: flex;
    flex-direction: column;
    gap: 20px;

    h2 {
        font-size: 3rem;
        font-weight: 500;
        color: var(--clr-accent)
    }

    p {
        color: #BFBFBF;

        a {
            color: inherit;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }
    }
}

.modal .button-container {
    display: flex;
    justify-content: center;
    align-items: center;

    width: 100%;
}

.modal .button-container button {
    min-width: 100%;
    padding: 4% 4%;

    background-color: #262626;

    border-radius: 63px;
    border: 1px solid #333;

    font-size: 1.125rem;
    color: #fff;

    cursor: pointer;
}
