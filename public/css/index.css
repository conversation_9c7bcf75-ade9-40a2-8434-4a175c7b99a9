/*
    Padding/Margin/Gap -> em
    W/H -> % or em
    font-size -> rem or em (Clamp)
  */



@media (width >=1054px) {
    .hero-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        height: 90vh;
    }

    .content-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        height: 100vh;
    }
}


/****************************************************************************/
/*----------------------------------Hero Section----------------------------*/
/****************************************************************************/


.hero-section {
    display: grid;
    grid-template-columns: 3fr 2fr;
    grid-template-rows: 1fr .2fr;
    grid-auto-rows: 1fr;

    column-gap: 5%;


    justify-content: center;

    background-color: #1A1A1A;

}

/*----------------------------------Title Section----------------------------*/

.hero-title-container {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: start;

    width: 100%;
    height: fit-content;
    gap: 90px;


}

.hero-title-text-container {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: 90px;
    width: 100%;
    height: fit-content;
}

.hero-title-container-title {
    font-size: 3rem;
    font-family: "Lexend";
    width: 100%;
    height: fit-content;
    color: white;
    line-height: 150%;
    font-weight: 500;
    margin: 0;

    .hero-title-container-span {
        color: #CAFF33;
    }
}

.hero-title-container-paragraph {
    font-size: 1rem;
    font-family: "Lexend";
    width: 100%;
    height: fit-content;
    color: #B3B3B3;
    line-height: 150%;
    font-weight: 300;
    margin: 0;
}

.hero-title-container-button {
    background-color: #CAFF33;
    border-radius: 82px;
    padding: 18px 30px;
    width: fit-content;
    height: fit-content;
    color: #1C1C1C;
    font-family: "Lexend";
    font-size: 1rem;
    line-height: 150%;
    text-align: center;

    cursor: pointer;
    text-decoration: none;
}

.hero-title-container-button:hover {
    background-color: #E4E4E7;
    color: #1C1C1C;
}

/*----------------------------------Transaction Section----------------------------*/

.hero-side-container {
    grid-column: 2 / 3;
    grid-row: 1 / 3;

    padding: 0 35px 35px 35px;

    gap: 80px;

    width: 100%;
    height: fit-content;

    background-image: url('../assets/svgs/hero-background.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 10px;

    h2 {
        color: white;
        font-family: "Lexend";
        font-size: 1.0625rem;
        font-weight: 500;
        margin: 0 0 14px 0;
    }
}

.hero-section-side-transactions {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;

    width: 100%;
    height: fit-content;

}

.hero-section-side-transaction-article {
    display: flex;
    justify-content: space-between;
    align-items: end;

    border-radius: 10px;

    padding: 14px 20px;

    width: 100%;
    height: fit-content;

    margin: 0 0 -15px 0;

    background-color: #1C1C1C;
    border: 1px solid #262626;

    p {
        margin: 0;
        color: white;
        font-family: "Lexend";
    }

    span {
        margin: 0;
        color: white;
        font-family: "Lexend";
    }
}

.hero-section-side-transaction-article-left-container {
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 9px;

    padding: 0;
}

.hero-section-side-transaction-article-img-container {
    width: fit-content;
    height: fit-content;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #CAFF33;
    border-radius: 45px;
    padding: 8.67px;

    img {
        width: 20px;
        height: 20px;
    }
}

.hero-section-side-transaction-article-text {
    display: flex;
    flex-direction: column;

    gap: 5px;

    width: fit-content;
    height: fit-content;
}

.section-side-transaction-text {
    font-size: 0.975625rem;
    font-weight: 300;
}

#transaction-top {
    opacity: 1;

    width: 100%;
    height: fit-content;

    z-index: 3;
}

#hero-section-side-transaction-article-name {
    font-size: 1.08375rem;
    font-weight: 400;

    width: fit-content;
}

#transaction-middle {
    opacity: 0.5;

    width: 95%;
    height: fit-content;

    z-index: 2;
}

#transaction-bottom {
    opacity: .2;

    width: 90%;
    height: fit-content;

    z-index: 1;
}

/*----------------------------------Money Section----------------------------*/

.hero-section-side-money-container {
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: center;

    width: 100%;
    height: fit-content;

    padding-top: 31px;

    gap: 25px;

    h2 {
        font-size: 1.0625rem;
        font-family: "Lexend";
        color: white;
        font-weight: 500;
        line-height: 150%;

        width: 100%;
        height: fit-content;

        text-align: left;
    }
}

.hero-section-side-money-articles {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: .5fr .5fr;
    grid-template-areas:
        'flag1 flag2'
        'money1 money2';

    width: 100%;
    height: fit-content;

}


.hero-section-side-money-top-article {
    display: flex;
    flex-direction: column;

    width: 100%;

    border: 1px solid #262626;


    padding: 17px;

    p {
        font-size: 0.875rem;
        font-family: "Lexend";
        line-height: 150%;
        color: #E4E4E7;
        font-weight: 300;


    }
}

.money-top-left {
    grid-area: flag1;
    border-top-left-radius: 10px;
}

.money-top-right {
    grid-area: flag2;
    border-top-right-radius: 10px;
}

.hero-section-side-money-image-container {
    display: flex;
    align-items: center;

    gap: 9px;

    width: 100%;
    height: fit-content;

    img {
        height: 34px;
        width: 34px;
    }

    span {
        color: #FFFFFF;
        text-transform: uppercase;

        font-size: 0.9375rem;
        font-family: "Lexend";

        line-height: 150%;
    }
}

.hero-section-side-money-bottom-article {
    display: flex;
    align-items: center;
    font-size: 1.0625rem;
    font-family: "Lexend";
    color: white;
    font-weight: 500;
    line-height: 150%;

    border: 1px solid #262626;

    padding: 26px 17px;
}

.money-bottom-right {
    grid-area: money1;
    border-bottom-left-radius: 10px;
}

.money-bottom-left {
    grid-area: money2;
    border-bottom-right-radius: 10px;
}

.hero-section-side-money-button {
    width: 100%;
    height: fit-content;

    padding: 14px 26px;
    border-radius: 71px;

    color: #CAFF33;
    background-color: #22251B;

    font-size: 1rem;
    font-family: "Lexend";
    line-height: 150%;
    text-align: center;

    cursor: pointer;

    text-decoration: none;
}

.hero-section-side-money-button:hover {
    color: #E4E4E7;  
}

@media (max-width: 972px) {
    .hero-section {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;

        width: 100%;
        height: fit-content;
    }

    .hero-title-container {
        grid-column: 1;
        grid-row: 1;

        display: flex;
        flex-direction: column;
        justify-content: start;
        align-items: center;

        text-align: center;

        padding: 5% 0;
    }

    .hero-side-container {
        grid-column: 1;
        grid-row: 2;
    }
}

@media (max-width: 390px) {
    .hero-section {
        width: 100%;
        height: 100%;

        padding-top: 0;
    }

    .hero-title-container {
        display: flex;
        flex-direction: column;
        justify-content: start;
        align-items: center;

        width: 100%;
        height: fit-content;

        /* padding: 20% 0; */
    }

    .hero-title-container-title {
        text-align: center;
        font-size: 2.5rem;
    }

    .hero-title-container-paragraph {
        text-align: center;
        font-size: 1rem;
    }
}

/****************************************************************************************************************/
/*----------------------------------------------Blockchain Meets Section----------------------------------------*/
/****************************************************************************************************************/



.blockchain-meets-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2em;
    padding: 2em;

    width: 100%;
}

.blockchain-meets-section img {
    object-fit: cover;
}

.blockchain-meets-section-info-container {
    display: flex;
    flex-direction: column;
    gap: 1%;

    p {
        color: #B3B3B3;
        font-size: 0.9375rem;
        font-size: clamp(0.9375rem, 0.9rem + 0.1875vw, 1.125rem);
        line-height: 150%;
        font-family: "Lexend";
        font-weight: 300;
    }
}

.blockchain-meets-section-info-heading-container {

    h1 {
        color: white;
        margin-bottom: 0;
        margin-top: 15px;
        line-height: 130%;
        font-family: "Lexend";
        font-weight: 500;
        font-size: 2rem;
        font-size: clamp(2rem, 1.8rem + 1vw, 3rem);
    }

    h2 {
        margin-bottom: 0;
        font-size: 0.8333333333333333rem;
        font-size: clamp(0.8333333333333333rem, 0.7499999999999999rem + 0.41666666666666674vw, 1.25rem);
        color: white;
        font-family: "Lexend";
        font-weight: 300;
    }

    span {
        color: #CAFF33;
    }
}

@media (max-width: 972px) {
    .blockchain-meets-section {
        flex-direction: column-reverse;
        justify-content: start;
        align-items: center;
        width: 100%;
        height: 100%;
    }

    .blockchain-meets-section img {
        width: 100%;
        height: 300px;
        object-fit: cover;
        object-position: center;
    }

    .blockchain-meets-section-info-container {
        p {
            text-align: center;
        }
    }

    .blockchain-meets-section-info-heading-container {
        h1 {
            text-align: center;
        }

        h2 {
            text-align: center;
        }
    }
}

/*******************************************************************************************************/
/*----------------------------------------------Tokenization Section-----------------------------------*/
/*******************************************************************************************************/

.tokenized-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
    width: 100%;

    gap: 2em;
}

.tokenized-section-title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
    width: 100%;
    height: fit-content;

    padding: 0 1em;

    h1 {
        margin: 0;


        color: white;
        font-size: 1.5rem;
        font-size: clamp(1.7rem, 1.2rem + 1.5vw, 3rem);
        font-family: "Lexend";
        font-weight: 500;
        text-align: center;
    }

    p {
        color: #B3B3B3;
        font-size: 0.75rem;
        font-size: clamp(0.95rem, 0.675rem + 0.375vw, 1.125rem);

        line-height: 150%;
        font-family: "Lexend";
        text-align: center;
    }

    span {
        color: #CAFF33;
    }
}

.tokenized-section-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 1em;
    width: 100%;

    padding: 0 1em;

    img {
        max-width: 100%;
        max-height: 100%;

        min-width: 187px;
        min-height: 178px;
        object-fit: cover;
    }
}


/*******************************************************************************************************/
/*------------------------------------------------CTA Section------------------------------------------*/
/*******************************************************************************************************/

.cta-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    height: 50vh;
}


.cta-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10em;

    padding: 3%;

    width: 100%;
    height: fit-content;

    background-color: #1C1C1C;

    border-radius: 20px;

    background-image: url('../assets/svgs/cta-background.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.cta-text-container {
    width: 100%;
    max-width: 845px;
    height: fit-content;

    h1 {
        color: white;
        font-size: 2.4rem;
        font-size: clamp(1.666666666666667rem, 1.5000000000000004rem + 0.8333333333333329vw, 2.5rem);
        font-family: "Lexend";
        line-height: 150%;

        width: 100%;
        height: fit-content;
    }

    p {
        color: #B3B3B3;
        font-size: 0.85rem;
        font-size: clamp(0.75rem, 0.675rem + 0.375vw, 1.125rem);
        line-height: 150%;
        font-family: "Lexend";
    }

    span {
        color: #CAFF33;
    }

}

.cta-button {
    min-width: 163px;
    height: fit-content;

    padding: 16px 3%;
    border-radius: 82px;

    background-color: #CAFF33;
    color: #1C1C1C;

    font-size: 0.75rem;
    font-size: clamp(0.75rem, 0.675rem + 0.375vw, 1.125rem);
    line-height: 150%;

    cursor: pointer;
    text-decoration: none;
    text-align: center;
}

.cta-button:hover {
    background-color: #A5D400;
}

@media (max-width: 768px) {
    .cta-section {
        flex-direction: column;
        gap: 1em;
        align-items: center;
    }

    .cta-text-container {
        text-align: center;
    }

    .cta-button {
        width: 100%;
    }
}

/*******************************************************************************************************/
/*----------------------------------------------About Section------------------------------------------*/
/*******************************************************************************************************/

.about-section {
    display: flex;

    padding-left: 1.25em;
    gap: 1.25em;
}

.about-section img{
    object-fit: cover;
}

.about-info-container {
    display: flex;
    flex-direction: column;
    justify-content: start;
    gap: 4.25em;

    padding: 1.25em;

}

.about-info-text{

    h1{
        margin-bottom: 1%;

        color: white;
        font-family: "Lexend";
        font-size: 1.923rem;
        font-size: clamp(1.923rem, 1.8076rem + 0.577vw, 2.5rem);
        font-weight: 500;
    }

    p {
        color: #B3B3B3;
        font-family: "Lexend";
        font-size: 0.865rem;
        font-size: clamp(0.865rem, 0.813rem + 0.26vw, 1.125rem);
        font-weight: 300;
    }
}

@media (max-width: 1022px){

    .about-section{
        flex-direction: column;

        padding: 0 1.25em;
    }

    .about-section img{
        height: 400px;
        width: 100%;
        object-fit: cover;
    }

    .about-info-container{
        gap: 1.25em;
        padding: 0;
    }

    .about-info-text{
        h1{
            text-align: center;
        }

        p{
            text-align: center;
        }
    }
}
