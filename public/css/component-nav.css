@import url('https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap');

*,
*::before,
*::after {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
}

.nav-container {
    width: 100%;
    height: 10vh;

    display: flex;
    justify-content: center;
    align-items: center;

    padding-top: 1em;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;

    padding: 1.25em 2.125em;

    width: 80%;
    height: 4.375em;

    background-color: #1C1C1C;
    /* box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); */

    border-radius: 100px;

    a {
        color: #E4E4E7;
        text-decoration: none;
        font-family: "Lexend";
        font-size: 1rem;
    }

    a:hover {
        /* color: #CAFF33; */
        text-decoration: underline;
    }
}

#nav-logo {
    text-decoration: none;
    font-family: "Lexend";
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 150%;

    color: #E4E4E7;
}

#nav-logo:hover {
    background: linear-gradient(90deg, #44E3FF, #2D87FF, #FC2FE5);
    background-size: 300%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: color-animation 3s ease infinite;
}



.nav-end-login-btn {
    font-size: 1rem;
    font-family: "Lexend";
    line-height: 150%;
    color: #1C1C1C;
    text-align: center;

    padding: 0.875em 1.875em;
    background-color: #CAFF33;
    border-radius: 82px;
    cursor: pointer;
    box-shadow: none;


}

@keyframes color-animation {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/*Hamburger*/

#nav-mid-container {
    display: flex;
    gap: 3.875em;
}

#nav-end-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#nav-end-container .nav-end-toggle-menu {
    /*Hide on 1440px screen*/
    display: none;
}

.nav-end-link-li {
    /*Hide on 1440px screen*/
    display: none;
}

.nav-end-link-li-btn {
    display: block;

}

.nav-menu {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 4;
    transition: 0.5s;

    /*Hide on 1440px screen*/
    padding: 0;

    gap: 1em;
}

.nav-menu li {
    list-style-type: none;
}

.nav-menu li a {
    display: block;
}

.nav-menu li button {
    display: block;
}

.nav-end-hamburger {
    position: relative;
    width: 30px;
    height: 4px;
    background-color: #CAFF33;
    border-radius: 10px;
    cursor: pointer;
    z-index: 5;
    transition: 0.3s;
}

.nav-end-hamburger::before,
.nav-end-hamburger::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 4px;
    right: 0;
    background-color: #CAFF33;
    border-radius: 10px;
    transition: 0.3s;

}

.nav-end-hamburger::before {
    top: -10px;
    width: 20px;
}

.nav-end-hamburger::after {
    top: 10px;
    width: 25px;
}

.nav-end-toggle-menu {
    position: absolute;
    width: 30px;
    height: 100%;
    z-index: 6;
    cursor: pointer;
    opacity: 0;
}

.nav-end-hamburger,
.nav-end-toggle-menu {
    display: none;
    /* position: fixed; */
}

#nav-end-container input:checked~.nav-end-hamburger {
    background: transparent;
}

#nav-end-container input:checked~.nav-end-hamburger:before {
    top: 0;
    transform: rotate(-45deg);
    width: 30px;
}

#nav-end-container input:checked~.nav-end-hamburger:after {
    top: 0;
    transform: rotate(45deg);
    width: 30px;
}

#nav-end-container input:checked~.nav-menu {
    right: 0;
}

/* Hamburger Menu */
@media screen and (max-width: 950px) {

    body .nav-container {
        /* background-color: #1C1C1C; */

        position: sticky;
        top: 0;

        z-index: 999;
    }

    #nav-mid-container {
        display: none;
    }

    #nav-end-container .nav-end-toggle-menu {
        display: block;
        height: 30px;
    }

    .nav-end-link-li {
        display: block;
    }

    .nav-end-hamburger,
    .nav-end-toggle-menu {
        display: block;
    }

    nav {
        padding: 10px 20px;
    }


    .nav-menu {
        justify-content: start;
        flex-direction: column;
        align-items: center;
        position: fixed;
        top: 0;
        right: -300px;
        background-color: #1C1C1C;
        width: 300px;
        height: 100%;
        padding-top: 65px;
    }


    .nav-menu li {
        width: 100%;
    }

    .nav-menu li,
    .nav-menu li {
        padding: 30px;
        box-shadow: 0 2px 0 #262626;
    }

    .nav-menu li button {
        width: 75%;
        margin-inline: auto;
        box-shadow: 0 4px 0 rgb(202, 255, 51, .4);
    }
}
