"use strict";
const navSecondaryButton = document.querySelector("#nav-secondary-a");
const navPrimaryButton = document.querySelector("#nav-primary-a");
fetch("/user")
    .then((response) => response.json())
    .then((_data) => {
    navSecondaryButton.innerText = "Marketplace";
    navSecondaryButton.href = "/marketplace";
    if (navPrimaryButton.parentElement) {
        navPrimaryButton.parentElement.style.maxWidth = "fit-content";
    }
    navPrimaryButton.innerText = "Dashboard";
    navPrimaryButton.href = "/dashboard";
})
    .catch(() => { });
