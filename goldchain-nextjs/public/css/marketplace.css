.marketplace-cards{
    display: flex;
    flex-direction: column;
    gap: 1.25em;
}

.card-container{
    display: flex;
    justify-content: center;

    padding: 3.75em;

    background-color: #1C1C1C;
}

.card-container img{
    object-fit: cover;
}

.card-container .info-container{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    background-color: #1A1A1A;

    gap: 10px;

    padding: 1.25em;

    h2{
        font-size: 2.5rem;
        color: #fff;
        font-weight: 500;
        font-family: "Lexend";
        text-align: center;
    }

    p{
        font-size: 1.125rem;
        font-family: "Lexend";
        font-weight: 300;
        color: #B3B3B3;
        text-align: justify;
    }
}

.info-container .button-container{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 0.625em;

    .secondary-button{
        padding: 0.875em 1.875em;
        background-color: #333333;
        border: none;
        color: #fff;
        border-radius: 82px;

        font-family: "Lexend";
        font-size: 1.125rem;
    }

    .primary-button{
        padding: 1em 1.875em;
        background-color: var(--clr-accent);
        border: none;
        color: #1C1C1C;
        border-radius: 82px;

        font-family: "Lexend";
        font-size: 1.125rem;

    }
}

@media (max-width: 1300px){
    .card-container{
        flex-direction: column;
        padding: 1.5em;
    }

    .card-container img{
        height: 400px;
        width: 100%;
        object-fit: cover;
    }

    .primary-button{
        width: 262px;
    }

    .secondary-button{
        width: 172px;
        padding: 0px 0px;

        text-align: center;
    }
}


@media (min-width: 1440px) {
    .card-container img {
        height: auto;
        width: 100%;
    }

    /* .card-container .info-container h2 {
        font-size: calc(2.5rem + (10 - 2.5) * ((100vw - 1440px) / (1920 - 1440)));
    }

    .card-container .info-container p {
        font-size: calc(1.125rem + (5 - 1.125) * ((100vw - 1440px) / (1920 - 1440)));
    }

    .info-container .button-container .secondary-button {
        font-size: calc(1.125rem + (5 - 1.125) * ((100vw - 1440px) / (1920 - 1440)));
    }

    .info-container .button-container .primary-button {
        font-size: calc(1.125rem + (5 - 1.125) * ((100vw - 1440px) / (1920 - 1440)));
    } */
}


.no-document .secondary-button{
    display: none !important;
} 
