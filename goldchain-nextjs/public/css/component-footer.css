@import url('https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap');

*,
*::before,
*::after {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
}

#footer-container{
    width: 100%;
    height: fit-content;

    background-color: #1C1C1C;

    padding: 6.25em 10.125em 3em 10.125em;
}

footer{
    display: flex;
    flex-direction: column;
    gap: 3.125em;
}

footer .top-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3.125em;


    #footer-logo{
        font-size: 1.25rem;
        font-family: "Lexend";
        font-weight: 700;
        line-height: 150%;

        color: #E4E4E7;

        text-decoration: none;
    }

    
}

footer .links-container{
    display: flex;
    gap: 1.625em;
    align-items: center;

    a{

        text-align: center;

        font-size: 1rem;
        font-family: "Lexend";
        font-weight: 400;
        line-height: 150%;
        color: #E4E4E7;

        text-decoration: none;
    }  
}

footer .links-container a:hover{
    color: #CAFF33;
}

footer .bottom-container{
    display: flex;
    justify-content: space-between;
    align-items: center;

    padding: 1em 1.875em 1em 1em;

    background-color: #1A1A1A;
    border-radius: 100px;
    border: 1px solid #262626;

    .social-media-buttons{
        display: flex;
        gap: 0.875em;
    }
}


footer .social-media-buttons .social-media-button{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.865em 0.885em;
    background-color: #CAFF33;
    border-radius: 100px;
}

footer .bottom-container .social-media-buttons .social-media-button a img {
    display: block;
    width: 24px;
    height: 24px;
}

footer .middle-container{
    color: #B3B3B3;
    font-family: "Lexend";
    font-size: 1rem;
    font-weight: 300;
    line-height: 150%;
}

footer .end-container{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: .5em;

    a{
        text-decoration: none;
        color: #B3B3B3;
        font-family: "Lexend";
        font-size: 1rem;
    }

    p{
        color: #B3B3B3;
        font-family: "Lexend";
    }

    #end-container-privacy{
        text-align: end;
    }

}

footer .end-container a:hover{
    color: #CAFF33;
}


@media (max-width: 1262px){
    #footer-container{
        padding: 3.125em 1em 1.875em 1em;
    }

    footer .bottom-container{
        flex-direction: column;
        border-radius: 12px;

        padding: 3.125em 1.5em 1.875em 1.5em;
        gap: 1.25em;
    }

    footer .middle-container p{
        text-align: center;
    }
}
