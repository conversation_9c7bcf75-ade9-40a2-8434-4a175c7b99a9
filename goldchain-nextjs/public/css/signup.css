main {
    width: 100%;
}

.form-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    gap: 1em;

    font-family: "Lexend";

    /* width: 1293px;
    height: 868px; */

    /* padding: 2em 10em; */

    background-image: url(../assets/images/backgrounds/modal-lg.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.text-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    gap: 1em;

    h1 {
        font-weight: 500;
        font-size: 3em;
        font-family: "Lexend";
    }

    p {
        color: #BFBFBF;
        font-weight: 300;
        font-size: 1.125em;
        text-align: center;
    }
}

.form-container {
    display: flex;
    flex-direction: column;
    gap: 2em;
}

form {

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    gap: 1em;
}

.input-row {
    display: flex;
    justify-content: center;
    align-items: center;

    gap: 1em;

    flex-wrap: wrap;

}

.input {
    display: grid;
    grid-template-columns: 1fr auto;

    /* width: 481px; */
    min-width: 420px;
    /* width: 100%; */
    padding: 1.5em;

    border-radius: 88px;
    border: 1px solid #262626;

    /* background-color: #1A1A1A; */

    input {
        background: transparent;
        outline: none;
        border: 0;
        /* width: 100%; */
        font-size: 1.25rem;

        color: #fff;
    }

    input::placeholder {
        font-family: "Lexend";
        color: #59595A;
    }
}

.password-input {
    img {
        cursor: pointer;
    }
}

.buttons-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    width: 100%;

    gap: 1em;
}

.button {
    font-size: 1.125em;
    border-radius: 63px;
    padding: 1em;
    width: 100%;

    outline: none;
    border: none;
    background: none;
    cursor: pointer;

    font-family: "Lexend";

    a {
        color: inherit;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }

}

.errors{
    color: red;
    display: none;
}

.primary-button {
    color: #000;
    background-color: var(--clr-accent);
}

.secondary-button {
    color: #fff;
    background-color: #262626;

    border: 1px solid #333;
}

/* width <= 855px */
@media (width < 954px){

    .input{
        width: 100%;
        min-width: 0;
    }

    .input input::placeholder {
        text-align: center;
    } 
}
