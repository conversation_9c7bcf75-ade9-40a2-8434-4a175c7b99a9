import db from "./database";
import bcrypt from "bcrypt";

// Temporary in-memory store for testing when Firebase is not available
const mockUsers: { [email: string]: User & { id: string } } = {};

// Create a test user for immediate testing
(async () => {
  const bcrypt = require("bcrypt");
  const hashedPassword = await bcrypt.hash("password123", 10);
  mockUsers["<EMAIL>"] = {
    id: "test-user-1",
    firstName: "Test",
    lastName: "User",
    email: "<EMAIL>",
    password: hashedPassword,
    phone: "1234567890",
    role: "user",
  };
})();

export type User = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  role: "admin" | "user";
};

export async function verifyPassword(email: string, password: string): Promise<boolean> {
  const user = await get(email);
  if (!user) {
    return false;
  }
  return await bcrypt.compare(password, user.password);
}

export async function get(email: string): Promise<User | undefined> {
  try {
    const snapshot = await db
      .collection("users")
      .where("email", "==", email)
      .get();

    const user = snapshot.docs[0]?.data() as User | undefined;
    return user;
  } catch (error) {
    console.log("Firebase not available, using mock store");
    return mockUsers[email];
  }
}

export async function getDocument(email: string): Promise<FirebaseFirestore.DocumentSnapshot | undefined> {
  try {
    const snapshot = await db
      .collection("users")
      .where("email", "==", email)
      .get();

    const document = snapshot.docs[0];
    return document;
  } catch (error) {
    console.log("Firebase not available, using mock store");
    return undefined; // Mock implementation doesn't need document
  }
}

export async function getId(email: string): Promise<string> {
  try {
    const userDocument = await getDocument(email);
    if (!userDocument) {
      throw new Error(`User with email ${email} not found`);
    }
    return userDocument.id;
  } catch (error) {
    console.log("Firebase not available, using mock store");
    const user = mockUsers[email];
    if (!user) {
      throw new Error(`User with email ${email} not found`);
    }
    return user.id;
  }
}

export async function create(
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  phone: number,
  role: string = "user",
): Promise<void> {
  const hashedPassword = await bcrypt.hash(password, 10);

  try {
    const docRef = db.collection("users").doc();
    await docRef.set({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      role,
    });
  } catch (error) {
    console.log("Firebase not available, using mock store");
    const userId = Date.now().toString();
    mockUsers[email] = {
      id: userId,
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone: phone.toString(),
      role: role as "admin" | "user",
    };
  }
}
