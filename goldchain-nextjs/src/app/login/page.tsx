import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

export default function Login() {
  return (
    <>
      <link rel="stylesheet" href="/css/signup.css" />
      <div className="wrapper">
        <Navigation />
        <main>
          <div className="signup-wrapper">
            <div className="signup-container">
              <div className="signup-title-container">
                <h1>Login</h1>
                <p>Welcome back! Please login to your account.</p>
              </div>
              <form action="/api/login" method="post">
                <div className="input-row">
                  <div className="input">
                    <input
                      type="email"
                      name="email"
                      id="email-input"
                      placeholder="Enter Email"
                      required
                    />
                  </div>
                </div>

                <div className="input-row">
                  <div className="input password-input">
                    <input
                      type="password"
                      name="password"
                      id="password-input"
                      placeholder="Enter Password"
                      required
                    />
                    <img
                      src="/assets/svgs/show-password-icon.svg"
                      alt=""
                    />
                  </div>
                </div>
                <span className="error-message"></span>
                <div className="buttons-container">
                  <button className="button primary-button" type="submit">Login</button>
                  <button className="button secondary-button" type="button">
                    <a href="/signup">Sign Up</a>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
}
