export default function Login() {
  return (
    <>
      <link rel="stylesheet" href="/css/default.css" />
      <link rel="stylesheet" href="/css/component-nav.css" />
      <link rel="stylesheet" href="/css/component-template.css" />
      <link rel="stylesheet" href="/css/component-footer.css" />
      <link rel="stylesheet" href="/css/signup.css" />

      <script type="module" src="/js/passwordInput.js" defer></script>
      <script type="module" src="/js/loggedin.js" defer></script>

      {/* START NAV COMPONENT */}
      <div className="nav-container">
        <nav>
          <a href="/" id="nav-logo">GoldChain DAO</a>

          <div id="nav-mid-container">
            <a href="/">Home</a>
            <a href="/#about">About</a>
          </div>

          <div id="nav-end-container">
            <input type="checkbox" className="nav-end-toggle-menu" />
            <div className="nav-end-hamburger"></div>

            <ul className="nav-menu">
              <li className="nav-end-link-li">
                <a className="nav-end-home-link" href="/">Home</a>
              </li>
              <li className="nav-end-link-li">
                <a className="nav-end-about-link" href="/#about">About</a>
              </li>
              <li className="nav-end-link-li-btn"><a href="/signup" id="nav-secondary-a">Sign Up</a></li>
              <li className="nav-end-link-li-btn">
                  <a href="/login" className="nav-end-login-btn" id="nav-primary-a">Login</a>
              </li>
            </ul>
          </div>
        </nav>
      </div>
      {/* END NAV COMPONENT */}

      <div className="wrapper">
        <main>
          <div className="form-wrapper">
            <div className="text-container">
              <h1>Login</h1>
              <p>Welcome back! Please log in to access your account.</p>
            </div>
            <div className="form-container">
              <form action="/api/login" method="post">
                <div className="input-row">
                  <div className="input">
                    <input
                      type="email"
                      name="email"
                      id="email-input"
                      maxLength={50}
                      placeholder="Enter your Email"
                      required
                    />
                  </div>
                  <div className="input password-input">
                    <input
                      type="password"
                      name="password"
                      id="password-input"
                      maxLength={255}
                      placeholder="Enter Password"
                      required
                    />
                    <img
                      src="assets/svgs/show-password-icon.svg"
                      alt=""
                    />
                  </div>
                </div>
                <div className="buttons-container">
                  <button className="button primary-button">Login</button>
                  <button className="button secondary-button">
                    <a href="/signup">Sign Up</a>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </main>
      </div>

      {/* START FOOTER COMPONENT */}
      <div id="footer-container">
        <footer>
          <div className="top-container">
            <a href="/" id="footer-logo">
              GoldChain DAO
            </a>
            <div className="links-container">
              <a href="/">Home</a>
              <a href="/#about">About</a>
            </div>
          </div>

          <div className="bottom-container">
            <div className="social-media-buttons">
              <div className="social-media-button">
                <a href="">
                  <img src="/assets/svgs/facebook-icon.svg" alt="Facebook" />
                </a>
              </div>
              <div className="social-media-button">
                <a href="">
                  <img src="/assets/svgs/twitter-icon.svg" alt="Twitter" />
                </a>
              </div>
              <div className="social-media-button">
                <a href="">
                  <img src="/assets/svgs/linkedin-icon.svg" alt="LinkedIn" />
                </a>
              </div>
            </div>

            <div className="middle-container">
              <p>Copyright © 2024 GoldChain DAO - All Rights Reserved</p>
            </div>

            <div className="end-container">
              <a href="" id="end-container-privacy">
                Privacy Policy
              </a>
              <p>|</p>
              <a href="">Terms of Service</a>
            </div>
          </div>
        </footer>
      </div>
      {/* END FOOTER COMPONENT */}
    </>
  );
}
