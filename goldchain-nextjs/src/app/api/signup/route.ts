import { NextRequest, NextResponse } from "next/server";
import { create, get } from "@/lib/user";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const firstName = formData.get("firstName") as string;
    const lastName = formData.get("lastName") as string;
    const email = formData.get("email") as string;
    const phone = formData.get("phone") as string;
    const password = formData.get("password") as string;

    if (await get(email)) {
      return NextResponse.redirect(new URL("/signup", request.url));
    }

    await create(firstName, lastName, email, password, parseInt(phone));

    return NextResponse.redirect(new URL("/login", request.url));
  } catch (error) {
    console.error("Signup error:", error);
    return NextResponse.redirect(new URL("/signup", request.url));
  }
}
