import { NextRequest, NextResponse } from "next/server";
import { get, verifyPassword } from "@/lib/user";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    console.log("Test login attempt for:", email);

    const user = await get(email);
    console.log("User found:", user ? "Yes" : "No");

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const passwordValid = await verifyPassword(email, password);
    console.log("Password valid:", passwordValid);

    return NextResponse.json({
      success: true,
      user: {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role
      },
      passwordValid
    });
  } catch (error) {
    console.error("Test login error:", error);
    return NextResponse.json({
      error: "Server error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Test login endpoint - use POST with { email, password }"
  });
}
