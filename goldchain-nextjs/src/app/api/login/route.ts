import { NextRequest, NextResponse } from "next/server";
import { get, getId, verifyPassword } from "@/lib/user";
import { createToken } from "@/lib/auth";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    const user = await get(email);

    if (!user || !(await verifyPassword(email, password))) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    const userId = await getId(user.email);

    const token = createToken({
      sub: userId,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      role: user.role,
    });

    const response = NextResponse.redirect(new URL("/dashboard", request.url));
    response.cookies.set("token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 3600, // 1 hour
      path: "/",
    });

    return response;
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.redirect(new URL("/login", request.url));
  }
}
