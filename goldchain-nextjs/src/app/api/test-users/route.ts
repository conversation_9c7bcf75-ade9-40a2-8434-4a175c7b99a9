import { NextResponse } from "next/server";
import { get } from "@/lib/user";

export async function GET() {
  try {
    // Test if we can get a user
    const testUser = await get("<EMAIL>");
    
    return NextResponse.json({
      message: "User test endpoint",
      testUser: testUser ? {
        firstName: testUser.firstName,
        lastName: testUser.lastName,
        email: testUser.email,
        role: testUser.role
      } : null
    });
  } catch (error) {
    return NextResponse.json({
      error: "Failed to get user",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
