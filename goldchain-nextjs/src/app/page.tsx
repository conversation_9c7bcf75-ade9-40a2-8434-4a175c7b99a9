export default function Home() {
  return (
    <>
      <link rel="stylesheet" href="/css/component-nav.css" />
      <link rel="stylesheet" href="/css/component-template.css" />
      <link rel="stylesheet" href="/css/component-footer.css" />
      <link rel="stylesheet" href="/css/default.css" />
      <link rel="stylesheet" href="/css/index.css" />

      <script type="module" src="/js/loggedin.js" defer></script>

      <div className="wrapper">
        {/* START NAV COMPONENT */}
        <div className="nav-container">
          <nav>
            <a href="/" id="nav-logo">GoldChain DAO</a>

            <div id="nav-mid-container">
              <a href="/">Home</a>
              <a href="/#about">About</a>
            </div>

            <div id="nav-end-container">
              <input type="checkbox" className="nav-end-toggle-menu" />
              <div className="nav-end-hamburger"></div>

              <ul className="nav-menu">
                <li className="nav-end-link-li">
                  <a className="nav-end-home-link" href="/">Home</a>
                </li>
                <li className="nav-end-link-li">
                  <a className="nav-end-about-link" href="/#about">About</a>
                </li>
                <li className="nav-end-link-li-btn"><a href="/signup" id="nav-secondary-a">Sign Up</a></li>
                <li className="nav-end-link-li-btn">
                    <a href="/login" className="nav-end-login-btn" id="nav-primary-a">Login</a>
                </li>
              </ul>
            </div>
          </nav>
        </div>
        {/* END NAV COMPONENT */}

        <main>
          {/*Start Hero Section*/}
          <div className="hero-wrapper">
            <section className="hero-section">
              {/*Left Side*/}
              <div className="hero-title-container">
                <div className="hero-title-text-container">
                  <h1 className="hero-title-container-title">
                    Welcome to GoldChain Dao Empowering Your
                    <span className="hero-title-container-span">Financial Journey</span>
                  </h1>
                  <p className="hero-title-container-paragraph">
                    At GoldChain Dao, our mission is to provide comprehensive RWA
                    Tokenization solutions that empower individuals and businesses
                    to achieve their financial goals. We are committed to delivering
                    personalized and innovative services that prioritize our
                    customers&apos; needs.
                  </p>
                </div>
                <a href="/marketplace" className="hero-title-container-button" id="open-account-hero-section-btn">Open Account</a>
              </div>
              {/*Right Side*/}
              <div className="hero-side-container">
                <h2>Your Transactions</h2>
                <div className="hero-section-side-transactions">
                  <article
                    className="hero-section-side-transaction-article"
                    id="transaction-top"
                  >
                    <div
                      className="hero-section-side-transaction-article-left-container"
                    >
                      <div
                        className="hero-section-side-transaction-article-img-container"
                      >
                        <img
                          src="./assets/svgs/transaction-icon.svg"
                          alt="Transaction Icon"
                        />
                      </div>
                      <div className="hero-section-side-transaction-article-text">
                        <p className="section-side-transaction-text">Transaction</p>
                        <p
                          id="side-transaction-article-top-name"
                          className="hero-section-side-transaction-article-name"
                        >
                          Jorge Parker
                        </p>
                      </div>
                    </div>
                    <span>-$78.00</span>
                  </article>

                  <article className="hero-section-side-transaction-article">
                    <div
                      className="hero-section-side-transaction-article-left-container"
                    >
                      <div
                        className="hero-section-side-transaction-article-img-container"
                      >
                        <img
                          src="./assets/svgs/transaction-icon.svg"
                          alt="Transaction Icon"
                        />
                      </div>
                      <div className="hero-section-side-transaction-article-text">
                        <p className="section-side-transaction-text">Transaction</p>
                        <p className="hero-section-side-transaction-article-name">
                          Samantha Williams
                        </p>
                      </div>
                    </div>
                    <span>+$2,456.00</span>
                  </article>

                  <article className="hero-section-side-transaction-article">
                    <div
                      className="hero-section-side-transaction-article-left-container"
                    >
                      <div
                        className="hero-section-side-transaction-article-img-container"
                      >
                        <img
                          src="./assets/svgs/transaction-icon.svg"
                          alt="Transaction Icon"
                        />
                      </div>
                      <div className="hero-section-side-transaction-article-text">
                        <p className="section-side-transaction-text">Transaction</p>
                        <p className="hero-section-side-transaction-article-name">
                          Michael Johnson
                        </p>
                      </div>
                    </div>
                    <span>-$1,200.00</span>
                  </article>

                  <article className="hero-section-side-transaction-article">
                    <div
                      className="hero-section-side-transaction-article-left-container"
                    >
                      <div
                        className="hero-section-side-transaction-article-img-container"
                      >
                        <img
                          src="./assets/svgs/transaction-icon.svg"
                          alt="Transaction Icon"
                        />
                      </div>
                      <div className="hero-section-side-transaction-article-text">
                        <p className="section-side-transaction-text">Transaction</p>
                        <p className="hero-section-side-transaction-article-name">
                          Emily Davis
                        </p>
                      </div>
                    </div>
                    <span>+$890.00</span>
                  </article>

                  <article className="hero-section-side-transaction-article">
                    <div
                      className="hero-section-side-transaction-article-left-container"
                    >
                      <div
                        className="hero-section-side-transaction-article-img-container"
                      >
                        <img
                          src="./assets/svgs/transaction-icon.svg"
                          alt="Transaction Icon"
                        />
                      </div>
                      <div className="hero-section-side-transaction-article-text">
                        <p className="section-side-transaction-text">Transaction</p>
                        <p className="hero-section-side-transaction-article-name">
                          David Brown
                        </p>
                      </div>
                    </div>
                    <span>-$345.00</span>
                  </article>
                </div>
              </div>
            </section>
          </div>
          {/*End Hero Section*/}

          {/*Start About Us*/}
          <div className="about-wrapper" id="about">
            <section className="about-section">
              <div className="about-title-container">
                <h1>About GoldChain DAO</h1>
                <p>
                  GoldChain DAO is a decentralized autonomous organization focused on
                  revolutionizing asset management through blockchain technology.
                </p>
              </div>
              <div className="about-info-container">
                <div className="about-info-text">
                  <h1>Asset Tokenization</h1>
                  <p>
                    Transform physical and digital assets into blockchain tokens,
                    enabling fractional ownership and increased liquidity.
                  </p>
                </div>
                <div className="about-info-text">
                  <h1>Decentralized Governance</h1>
                  <p>
                    Community-driven decision making through our DAO structure,
                    ensuring transparency and democratic participation.
                  </p>
                </div>
                <div className="about-info-text">
                  <h1>Secure Trading</h1>
                  <p>
                    Advanced blockchain security protocols protect your investments
                    and ensure safe, transparent transactions.
                  </p>
                </div>
                <div className="about-info-text">
                  <h1>Carbon Credits & Offsets</h1>
                  <p>
                    Utilize your land or underground assets as a "vault" and monetize in-ground assets and carbon
                    credits or offsets.
                  </p>
                </div>
                <div className="about-info-text">
                  <h1>Anti-Counterfeiting Gold NFT&apos;s</h1>
                  <p>True Community Voting Power and Purchasing Power.</p>
                </div>
              </div>
            </section>
          </div>
          {/*End About Us*/}
        </main>

        {/* START FOOTER COMPONENT */}
        <div id="footer-container">
          <footer>
            <div className="top-container">
              <a href="/" id="footer-logo">
                GoldChain DAO
              </a>
              <div className="links-container">
                <a href="/">Home</a>
                <a href="/#about">About</a>
              </div>
            </div>

            <div className="bottom-container">
              <div className="social-media-buttons">
                <div className="social-media-button">
                  <a href="">
                    <img src="/assets/svgs/facebook-icon.svg" alt="Facebook" />
                  </a>
                </div>
                <div className="social-media-button">
                  <a href="">
                    <img src="/assets/svgs/twitter-icon.svg" alt="Twitter" />
                  </a>
                </div>
                <div className="social-media-button">
                  <a href="">
                    <img src="/assets/svgs/linkedin-icon.svg" alt="LinkedIn" />
                  </a>
                </div>
              </div>

              <div className="middle-container">
                <p>Copyright © 2024 GoldChain DAO - All Rights Reserved</p>
              </div>

              <div className="end-container">
                <a href="" id="end-container-privacy">
                  Privacy Policy
                </a>
                <p>|</p>
                <a href="">Terms of Service</a>
              </div>
            </div>
          </footer>
        </div>
        {/* END FOOTER COMPONENT */}
      </div>
    </>
  );
}
