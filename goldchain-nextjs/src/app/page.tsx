export default function Home() {
  return (
    <>
      <link rel="stylesheet" href="/css/component-nav.css" />
      <link rel="stylesheet" href="/css/component-template.css" />
      <link rel="stylesheet" href="/css/component-footer.css" />
      <link rel="stylesheet" href="/css/default.css" />
      <link rel="stylesheet" href="/css/index.css" />

      <script type="module" src="/js/loggedin.js" defer></script>

      <div className="wrapper">
        {/* START NAV COMPONENT */}
        <div className="nav-container">
          <nav>
            <a href="/" id="nav-logo">GoldChain DAO</a>

            <div id="nav-mid-container">
              <a href="/">Home</a>
              <a href="/#about">About</a>
            </div>

            <div id="nav-end-container">
              <input type="checkbox" className="nav-end-toggle-menu" />
              <div className="nav-end-hamburger"></div>

              <ul className="nav-menu">
                <li className="nav-end-link-li">
                  <a className="nav-end-home-link" href="/">Home</a>
                </li>
                <li className="nav-end-link-li">
                  <a className="nav-end-about-link" href="/#about">About</a>
                </li>
                <li className="nav-end-link-li-btn"><a href="/signup" id="nav-secondary-a">Sign Up</a></li>
                <li className="nav-end-link-li-btn">
                    <a href="/login" className="nav-end-login-btn" id="nav-primary-a">Login</a>
                </li>
              </ul>
            </div>
          </nav>
        </div>
        {/* END NAV COMPONENT */}

        <main>
          {/*Start Hero Section*/}
          <div className="hero-wrapper">
            <section className="hero-section">
              {/*Left Side*/}
              <div className="hero-title-container">
                <div className="hero-title-text-container">
                  <h1 className="hero-title-container-title">
                    Welcome to GoldChain Dao Empowering Your
                    <span className="hero-title-container-span">Financial Journey</span>
                  </h1>
                  <p className="hero-title-container-paragraph">
                    At GoldChain Dao, our mission is to provide comprehensive RWA
                    Tokenization solutions that empower individuals and businesses
                    to achieve their financial goals. We are committed to delivering
                    personalized and innovative services that prioritize our
                    customers&apos; needs.
                  </p>
                </div>
                <a href="/marketplace" className="hero-title-container-button" id="open-account-hero-section-btn">Open Account</a>
              </div>
              {/*Right Side*/}
              <div className="hero-side-container">
                <h2>Your Transactions</h2>
                <div className="hero-section-side-transactions">
                  <article
                    className="hero-section-side-transaction-article"
                    id="transaction-top"
                  >
                    <div
                      className="hero-section-side-transaction-article-left-container"
                    >
                      <div
                        className="hero-section-side-transaction-article-img-container"
                      >
                        <img
                          src="./assets/svgs/transaction-icon.svg"
                          alt="Transaction Icon"
                        />
                      </div>
                      <div className="hero-section-side-transaction-article-text">
                        <p className="section-side-transaction-text">Transaction</p>
                        <p
                          id="side-transaction-article-top-name"
                          className="hero-section-side-transaction-article-name"
                        >
                          Jorge Parker
                        </p>
                      </div>
                    </div>
                    <span>-$78.00</span>
                  </article>

                <article
                  className="hero-section-side-transaction-article"
                  id="transaction-middle"
                >
                  <div
                    className="hero-section-side-transaction-article-left-container"
                  >
                    <div
                      className="hero-section-side-transaction-article-img-container"
                    >
                      <img
                        src="./assets/svgs/transaction-icon.svg"
                        alt="Transaction Icon"
                      />
                    </div>
                    <div className="hero-section-side-transaction-article-text">
                      <p className="section-side-transaction-text">Transaction</p>
                      <p
                        id="side-transaction-article-middle-name"
                        className="hero-section-side-transaction-article-name"
                      >
                        Maxwell Walker
                      </p>
                    </div>
                  </div>
                  <span>-$100.00</span>
                </article>
                <article
                  className="hero-section-side-transaction-article"
                  id="transaction-bottom"
                >
                  <div
                    className="hero-section-side-transaction-article-left-container"
                  >
                    <div
                      className="hero-section-side-transaction-article-img-container"
                    >
                      <img
                        src="./assets/svgs/transaction-icon.svg"
                        alt="Transaction Icon"
                      />
                    </div>
                    <div className="hero-section-side-transaction-article-text">
                      <p className="section-side-transaction-text">Transaction</p>
                      <p
                        id="side-transaction-article-bottom-name"
                        className="hero-section-side-transaction-article-name"
                      >
                        Kenneth Carter
                      </p>
                    </div>
                  </div>
                  <span>-$50.00</span>
                </article>
                </div>

              {/*Money Section*/}
              <div className="hero-section-side-money-container">
                <h2>Money Exchange</h2>
                <div className="hero-section-side-money-articles">
                  <div className="hero-section-side-money-top-article money-top-left">
                    <div className="hero-section-side-money-image-container">
                      <img src="./assets/images/india-flag.png" /><span>Inr</span>
                    </div>
                    <p>Indian Rupees</p>
                  </div>
                  <div
                    className="hero-section-side-money-top-article money-top-right"
                  >
                    <div className="hero-section-side-money-image-container">
                      <img src="./assets/images/us-flag.png" /><span>Usd</span>
                    </div>
                    <p>United States Dollar</p>
                  </div>
                  <div
                    className="hero-section-side-money-bottom-article money-bottom-left"
                  >
                    <p>5,0000</p>
                  </div>
                  <div
                    className="hero-section-side-money-bottom-article money-bottom-right"
                  >
                    <p>12.00</p>
                  </div>
                </div>
                <a href="/marketplace" className="hero-section-side-money-button" id="exchange-hero-section-btn">Exchange</a>
              </div>
              </div>
            </section>
          </div>
          {/*End Hero Section*/}

        {/*Start Block Chain Meets Section*/}
        <div className="content-wrapper">
          <section className="blockchain-meets-section">
            <div className="blockchain-meets-section-info-container">
              <div className="blockchain-meets-section-info-heading-container">
                <h2>Welcome to GoldChain Dao</h2>
                <h1>Where Blockchain Meets <span>Excellence!</span></h1>
              </div>
              <p>
                At GoldChain DAO, we are revolutionizing how assets are managed,
                empowering individuals and businesses to harness the potential of
                blockchain technology. Our mission goes beyond offering
                traditional financial services — we provide innovative tokenized
                solutions that bridge the gap between real-world assets and
                decentralized finance. <br />
                <br />
                With a steadfast commitment to transparency, security, and
                accessibility, we enable participants to engage with a wide range
                of tokenized assets, from precious metals to real estate, all
                backed by cutting-edge blockchain technology. At GoldChain DAO, we
                aim to reshape the future of finance by delivering seamless,
                secure, and impactful solutions that empower you to take control
                of your financial destiny.
                <br />
                <br />
                Join us as we unlock new possibilities in the world of tokenized
                assets and experience a future where finance meets innovation.
              </p>
            </div>
            <img
              src="./assets/images/blockchain-meets.png"
              alt="Blockchain Meets"
            />
          </section>
        </div>
        {/*End Block Chain Meets Section*/}

        {/*Start Tokenized RWA Section*/}
        <div className="content-wrapper">
          <section className="tokenized-section">
            <div className="tokenized-section-title-container">
              <h1><span>Tokenized</span> Real World Assets on GoldChain DAO</h1>
              <p>
                Discover a range of comprehensive and customizable Tokenized asset
                products at GoldChain Dao, designed to suit your unique financial
                aspirations
              </p>
            </div>
            <div className="tokenized-section-content">
              <img
                src="./assets/images/tokenization-wilson-investment.png"
                alt=""
              />
              <img
                src="./assets/images/tokenization-livelabels-logo.png"
                alt=""
              />
              <img
                src="./assets/images/tokenization-livelabels-qrcode.png"
                alt=""
              />
              <img src="./assets/images/tokenization-gt-enterprises.png" alt="" />
              <img src="./assets/images/tokenization-the-giant.png" alt="" />
              <img src="./assets/images/tokenization-gold-chain-dao.png" alt="" />
            </div>
          </section>
        </div>
        {/*End Tokenized RWA Section*/}

        {/*Start CTA Section*/}
        <div className="cta-wrapper">
          <section className="cta-section">
            <div className="cta-text-container">
              <h1>
                Start your financial journey with
                <span>GoldChain Dao today!</span>
              </h1>
              <p>
                Ready to take control of your finances? Join GoldChain Dao now,
                and let us help you achieve your financial goals with our tailored
                solutions and exceptional customer service.
              </p>
            </div>
            <a href="/marketplace" className="cta-button" id="cta-section-btn-link">Get Started</a>
          </section>
        </div>
        {/*End CTA Section*/}

        {/*Start About Us*/}
        <div className="content-wrapper">
          <section className="about-section" id="about">
            <img src="./assets/images/marketplace/gold.png" />
            <div className="about-info-container">
              <div className="about-info-text">
                <h1>About Us</h1>
                <p>
                  In todays NFT world, GoldChain DAO sets a standard of
                  transparency.
                </p>
              </div>
              <div className="about-info-text">
                <h1>Invest in the Future of Gold</h1>
                <p>
                  GoldChain DAO saves mother earth by assigning carbon credits on
                  the US Carbon Exchange &amp; Vault, generating millions for
                  precious metal mining companies to leave the resources in the
                  ground as a "vault" and monetize in-ground assets and carbon
                  credits or offsets.
                </p>
              </div>
              <div className="about-info-text">
                <h1>Anti-Counterfeiting Gold NFT&apos;s</h1>
                <p>True Community Voting Power and Purchasing Power.</p>
              </div>
            </div>
          </section>
        </div>
        {/*End About Us*/}
        </main>

        {/* START FOOTER COMPONENT */}
        <div id="footer-container">
          <footer>
            <div className="top-container">
              <a href="/" id="footer-logo">
                GoldChain DAO
              </a>
              <div className="links-container">
                <a href="/">Home</a>
                <a href="/#about">About</a>
              </div>
            </div>

            <div className="bottom-container">
              <div className="social-media-buttons">
                <div className="social-media-button">
                  <a href="">
                    <img src="/assets/svgs/facebook-icon.svg" alt="Facebook" />
                  </a>
                </div>
                <div className="social-media-button">
                  <a href="">
                    <img src="/assets/svgs/twitter-icon.svg" alt="Twitter" />
                  </a>
                </div>
                <div className="social-media-button">
                  <a href="">
                    <img src="/assets/svgs/linkedin-icon.svg" alt="LinkedIn" />
                  </a>
                </div>
              </div>

              <div className="middle-container">
                <p>Copyright © 2024 GoldChain DAO - All Rights Reserved</p>
              </div>

              <div className="end-container">
                <a href="" id="end-container-privacy">
                  Privacy Policy
                </a>
                <p>|</p>
                <a href="">Terms of Service</a>
              </div>
            </div>
          </footer>
        </div>
        {/* END FOOTER COMPONENT */}
      </div>
    </>
  );
}
