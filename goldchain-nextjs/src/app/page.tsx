import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import Image from "next/image";

export default function Home() {
  return (
    <>
      <link rel="stylesheet" href="/css/index.css" />
      <div className="wrapper">
        <Navigation />
        <main>
          {/* Start Hero Section */}
          <div className="hero-wrapper">
            <section className="hero-section">
              {/* Left Side */}
              <div className="hero-title-container">
                <div className="hero-title-text-container">
                  <h1 className="hero-title-container-title">
                    Welcome to GoldChain Dao Empowering Your
                    <span className="hero-title-container-title-span"> Financial Future</span>
                  </h1>
                  <p className="hero-title-container-description">
                    At GoldChain DAO, we are revolutionizing how assets are managed,
                    tokenized, and traded. Our platform leverages blockchain technology
                    to provide secure, transparent, and efficient solutions for asset
                    management and investment opportunities.
                  </p>
                </div>
                <div className="hero-title-buttons-container">
                  <a href="/signup" className="button primary-button">
                    Get Started
                  </a>
                  <a href="#about" className="button secondary-button">
                    Learn More
                  </a>
                </div>
              </div>
              {/* Right Side */}
              <div className="hero-image-container">
                <Image
                  src="/assets/images/hero-image.png"
                  alt="Hero"
                  width={500}
                  height={400}
                  priority
                />
              </div>
            </section>
          </div>
          {/* End Hero Section */}

          {/* Start About Us */}
          <div className="about-wrapper" id="about">
            <section className="about-section">
              <div className="about-title-container">
                <h1>About GoldChain DAO</h1>
                <p>
                  GoldChain DAO is a decentralized autonomous organization focused on
                  revolutionizing asset management through blockchain technology.
                </p>
              </div>
            </section>
          </div>
          {/* End About Us */}
        </main>
        <Footer />
      </div>
    </>
  );
}
