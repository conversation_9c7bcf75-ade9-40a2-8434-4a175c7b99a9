"use client";

import { useState } from "react";

export default function TestLogin() {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("password123");
  const [result, setResult] = useState("");

  const testLogin = async () => {
    try {
      const response = await fetch("/api/test-login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setResult("Error: " + error);
    }
  };

  const actualLogin = async () => {
    try {
      const formData = new FormData();
      formData.append("email", email);
      formData.append("password", password);

      const response = await fetch("/api/login", {
        method: "POST",
        body: formData,
      });

      if (response.redirected) {
        setResult("Redirected to: " + response.url);
        window.location.href = response.url;
      } else {
        const text = await response.text();
        setResult("Response: " + text);
      }
    } catch (error) {
      setResult("Error: " + error);
    }
  };

  return (
    <div style={{ padding: "20px", fontFamily: "monospace" }}>
      <h1>Login Test Page</h1>
      
      <div style={{ marginBottom: "20px" }}>
        <div>
          <label>Email: </label>
          <input 
            type="email" 
            value={email} 
            onChange={(e) => setEmail(e.target.value)}
            style={{ width: "200px", padding: "5px" }}
          />
        </div>
        <div style={{ marginTop: "10px" }}>
          <label>Password: </label>
          <input 
            type="password" 
            value={password} 
            onChange={(e) => setPassword(e.target.value)}
            style={{ width: "200px", padding: "5px" }}
          />
        </div>
      </div>

      <div style={{ marginBottom: "20px" }}>
        <button onClick={testLogin} style={{ marginRight: "10px", padding: "10px" }}>
          Test Login API
        </button>
        <button onClick={actualLogin} style={{ padding: "10px" }}>
          Actual Login
        </button>
      </div>

      <div>
        <h3>Result:</h3>
        <pre style={{ background: "#f0f0f0", padding: "10px", whiteSpace: "pre-wrap" }}>
          {result}
        </pre>
      </div>
    </div>
  );
}
