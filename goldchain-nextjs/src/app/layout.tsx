import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "GoldChain DAO",
  description: "Empowering Your Financial Future",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="stylesheet" href="/css/default.css" />
        <link rel="stylesheet" href="/css/component-nav.css" />
        <link rel="stylesheet" href="/css/component-template.css" />
        <link rel="stylesheet" href="/css/component-footer.css" />
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
