import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

export default function Signup() {
  return (
    <>
      <link rel="stylesheet" href="/css/signup.css" />
      <div className="wrapper">
        <Navigation />
        <main>
          <div className="signup-wrapper">
            <div className="signup-container">
              <div className="signup-title-container">
                <h1>Sign Up</h1>
                <p>Create your account to get started with GoldChain DAO.</p>
              </div>
              <form action="/api/signup" method="post">
                <div className="input-row">
                  <div className="input">
                    <input
                      type="text"
                      name="firstName"
                      id="first-name-input"
                      placeholder="Enter First Name"
                      required
                    />
                  </div>

                  <div className="input">
                    <input
                      type="text"
                      name="lastName"
                      id="last-name-input"
                      placeholder="Enter Last Name"
                      required
                    />
                  </div>
                </div>

                <div className="input-row">
                  <div className="input">
                    <input
                      type="email"
                      name="email"
                      id="email-input"
                      placeholder="Enter Email"
                      required
                    />
                  </div>
                  <div className="input">
                    <input
                      type="tel"
                      name="phone"
                      id="tel-input"
                      title="************"
                      maxLength={15}
                      placeholder="Enter Phone Number"
                      required
                    />
                  </div>
                </div>

                <div className="input-row">
                  <div className="input password-input">
                    <input
                      type="password"
                      name="password"
                      id="password-input"
                      placeholder="Enter Password"
                      required
                    />
                    <img
                      src="/assets/svgs/show-password-icon.svg"
                      alt=""
                    />
                  </div>
                  <div className="input password-input">
                    <input
                      type="password"
                      name="confirmPassword"
                      id="confirm-password-input"
                      placeholder="Confirm Password"
                      required
                    />
                    <img
                      src="/assets/svgs/show-password-icon.svg"
                      alt=""
                    />
                  </div>
                </div>
                <span className="error-message"></span>
                <div className="buttons-container">
                  <button className="button primary-button" type="submit">Sign Up</button>
                  <button className="button secondary-button" type="button">
                    <a href="/login">Login</a>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
}
