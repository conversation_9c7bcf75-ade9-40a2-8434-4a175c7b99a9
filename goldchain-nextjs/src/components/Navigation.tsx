"use client";

import { useEffect, useState } from "react";
import Link from "next/link";

interface User {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export default function Navigation() {
  const [, setUser] = useState<User | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    fetch("/api/user")
      .then((response) => {
        if (response.ok) {
          return response.json();
        }
        throw new Error("Not logged in");
      })
      .then((data: User) => {
        setUser(data);
        setIsLoggedIn(true);
      })
      .catch(() => {
        setIsLoggedIn(false);
      });
  }, []);

  return (
    <div className="nav-container">
      <nav>
        <Link href="/" id="nav-logo">
          GoldChain DAO
        </Link>

        <div id="nav-mid-container">
          <Link href="/">Home</Link>
          <Link href="/#about">About</Link>
        </div>

        <div id="nav-end-container">
          <input type="checkbox" className="nav-end-toggle-menu" />
          <div className="nav-end-hamburger"></div>

          <ul className="nav-menu">
            <li className="nav-end-link-li">
              <Link className="nav-end-home-link" href="/">
                Home
              </Link>
            </li>
            <li className="nav-end-link-li">
              <Link className="nav-end-about-link" href="/#about">
                About
              </Link>
            </li>
            <li className="nav-end-link-li-btn">
              <Link href={isLoggedIn ? "/marketplace" : "/signup"} id="nav-secondary-a">
                {isLoggedIn ? "Marketplace" : "Sign Up"}
              </Link>
            </li>
            <li className="nav-end-link-li-btn">
              <Link
                href={isLoggedIn ? "/dashboard" : "/login"}
                className="nav-end-login-btn"
                id="nav-primary-a"
                style={isLoggedIn ? { maxWidth: "fit-content" } : {}}
              >
                {isLoggedIn ? "Dashboard" : "Login"}
              </Link>
            </li>
          </ul>
        </div>
      </nav>
    </div>
  );
}
