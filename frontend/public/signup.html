<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link rel="stylesheet" href="css/default.css" />
    <link rel="stylesheet" href="css/component-nav.css" />
    <link rel="stylesheet" href="css/component-template.css" />
    <link rel="stylesheet" href="css/component-footer.css" />
    <link rel="stylesheet" href="css/signup.css" />

    <script type="module" src="js/signup.js" defer></script>
    <script type="module" src="js/passwordInput.js" defer></script>
    <script type="module" src="js/loggedin.js" defer></script>
    <title>Sign Up</title>
  </head>
  <body>
    <div class="wrapper">
    <!-- START NAV COMPONENT -->
    <div class="nav-container">
      <nav>
        <a href="/" id="nav-logo">GoldChain DAO</a>

        <div id="nav-mid-container">
          <a href="/">Home</a>
          <a href="/#about">About</a>
        </div>

        <div id="nav-end-container">
          <input type="checkbox" class="nav-end-toggle-menu" />
          <div class="nav-end-hamburger"></div>

          <ul class="nav-menu">
            <li class="nav-end-link-li">
              <a class="nav-end-home-link" href="/">Home</a>
            </li>
            <li class="nav-end-link-li">
              <a class="nav-end-about-link" href="/#about">About</a>
            </li>
            <li class="nav-end-link-li-btn"><a href="/signup" id="nav-secondary-a">Sign Up</a></li>
            <li class="nav-end-link-li-btn">
                <!-- <button class="nav-end-login-btn"> -->
                  <a href="/login" class="nav-end-login-btn" id="nav-primary-a">Login</a>
                <!-- </button> -->
            </li>
          </ul>
        </div>
      </nav>
    </div>
    <!-- END NAV COMPONENT -->
      <main>
        <div class="form-wrapper">
          <div class="text-container">
            <h1>Sign Up</h1>
            <p>
              Join our community today!<br /><br />
              Create an account to unlock exclusive features and personalized
              experiences.
            </p>
          </div>
          <div class="form-container">
            <form action="/signup" method="post">
              <div class="input-row">
                <div class="input">
                  <input
                    type="text"
                    name="firstName"
                    id="first-name-input"
                    placeholder="Enter First Name"
                    required
                  />
                </div>

                <div class="input">
                  <input
                    type="text"
                    name="lastName"
                    id="last-name-input"
                    placeholder="Enter Last Name"
                    required
                  />
                </div>
              </div>

              <div class="input-row">
                <div class="input">
                  <input
                    type="email"
                    name="email"
                    id="email-input"
                    placeholder="Enter Email"
                    required
                  />
                </div>
                <div class="input">
                  <input
                    type="tel"
                    name="phone"
                    id="tel-input"
                    title="************"
                    maxlength="15"
                    placeholder="Enter Phone Number"
                    required
                  />
              </div>

              </div>

              <div class="input-row">
                <div class="input password-input">
                  <input
                    type="password"
                    name="password"
                    id="password-input"
                    placeholder="Enter Password"
                    required
                  />
                  <img
                    src="assets/svgs/show-password-icon.svg"
                    alt=""
                    srcset=""
                  />
                </div>
                <div class="input password-input">
                  <input
                    type="password"
                    name="confirmPassword"
                    id="confirm-password-input"
                    placeholder="Confirm Password"
                    required
                  />
                  <img
                    src="assets/svgs/show-password-icon.svg"
                    alt=""
                    srcset=""
                  />
                </div>
              </div>
              <span class="error-message"></span>
              <div class="buttons-container">
                <button class="button primary-button" type="submit">Sign Up</button>
                <button class="button secondary-button">
                  <a href="/login">Login</a>
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>

      <!-- START FOOTER COMPONENT  -->
    <div id="footer-container">
      <footer>
        <div class="top-container">
          <a href="/" id="footer-logo">GoldChain DAO</a>
          <div class="links-container">
            <a href="/">Home</a>
            <a href="/#about">About</a>
          </div>
        </div>

        <div class="bottom-container">
          <div class="social-media-buttons">
            <div class="social-media-button">
              <a href="">
                <img src="assets/svgs/facebook-icon.svg" alt="" srcset="" />
              </a>
            </div>
            <div class="social-media-button">
              <a href="">
                <img src="assets/svgs/twitter-icon.svg" alt="" srcset="" />
              </a>
            </div>
            <div class="social-media-button">
              <a href="">
                <img src="assets/svgs/linkedin-icon.svg" alt="" srcset="" />
              </a>
            </div>
          </div>

          <div class="middle-container">
            <p>Copyright © 2024 GoldChain DAO - All Rights Reserved</p>
          </div>

          <div class="end-container">
            <a href="" id="end-container-privacy">Privacy Policy</a>
            <p>|</p>
            <a href="">Terms of Service</a>
          </div>
        </div>
      </footer>
    </div>

    <!-- END FOOTER COMPONENT  -->
    </div>
  </body>
</html>
