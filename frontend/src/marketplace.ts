const gcdDocumentsButton = document.querySelector(
  "#gcd-document-button",
) as HTMLButtonElement;
const igrowpodDocumentsButton = document.querySelector(
  "#igrowpod-document-button",
) as HTMLButtonElement;
const roicreDocumentsButton = document.querySelector(
  "#roicre-document-button",
) as HTMLButtonElement;
const thegiantDocumentsButton = document.querySelector(
  "#thegiant-document-button",
) as HTMLButtonElement;

gcdDocumentsButton.addEventListener("click", () => {
  window.open("/documents/GoldChain_DAO_General_Presentation");
});

igrowpodDocumentsButton.addEventListener("click", () => {
  window.open("documents/IGrowPodSalesPack2023");
});

roicreDocumentsButton.addEventListener("click", () => {
  window.open("documents/<PERSON>_<PERSON>_&_Las_Vegas_Blvd_s-swc");
});

thegiantDocumentsButton.addEventListener("click", () => {
  window.open("documents/TGMC_Partner_Proposal_2024_Final");
});

/* Schedule to Tokenize */
// need React to simplify this.

const gcdScheduleButton = document.querySelector(
  "#gcd-schedule-button",
) as HTMLButtonElement;
const gcdModal = document.querySelector("#gcd-modal") as HTMLDialogElement;

gcdScheduleButton.addEventListener("click", () => {
  gcdModal.style.display = "flex";
  gcdModal.showModal();
});

const gcdModalButton = gcdModal.querySelector(".button-container button");
if (gcdModalButton) {
  gcdModalButton.addEventListener("click", () => {
    gcdModal.close();
    gcdModal.style.display = "none";
  });
}

const igrowpodScheduleButton = document.querySelector(
  "#igrowpod-schedule-button",
) as HTMLButtonElement;
const igrowpodModal = document.querySelector(
  "#igrowpod-modal",
) as HTMLDialogElement;

igrowpodScheduleButton.addEventListener("click", () => {
  igrowpodModal.style.display = "flex";
  igrowpodModal.showModal();
});

const igrowpodModalButton = igrowpodModal.querySelector(".button-container button");
if (igrowpodModalButton) {
  igrowpodModalButton.addEventListener("click", () => {
    igrowpodModal.close();
    igrowpodModal.style.display = "none";
  });
}

const roicreScheduleButton = document.querySelector(
  "#roicre-schedule-button",
) as HTMLButtonElement;
const roicreModal = document.querySelector(
  "#roicre-modal",
) as HTMLDialogElement;

roicreScheduleButton.addEventListener("click", () => {
  roicreModal.style.display = "flex";
  roicreModal.showModal();
});

const roicreModalButton = roicreModal.querySelector(".button-container button");
if (roicreModalButton) {
  roicreModalButton.addEventListener("click", () => {
    roicreModal.close();
    roicreModal.style.display = "none";
  });
}

const thegiantScheduleButton = document.querySelector(
  "#thegiant-schedule-button",
) as HTMLButtonElement;
const thegiantModal = document.querySelector(
  "#thegiant-modal",
) as HTMLDialogElement;

thegiantScheduleButton.addEventListener("click", () => {
  thegiantModal.style.display = "flex";
  thegiantModal.showModal();
});

const thegiantModalButton = thegiantModal.querySelector(".button-container button");
if (thegiantModalButton) {
  thegiantModalButton.addEventListener("click", () => {
    thegiantModal.close();
    thegiantModal.style.display = "none";
  });
}

const wilsoninvestmentsScheduleButton = document.querySelector(
  "#wilsoninvestments-schedule-button",
) as HTMLButtonElement;
const wilsoninvestmentsModal = document.querySelector(
  "#wilsoninvestments-modal",
) as HTMLDialogElement;

wilsoninvestmentsScheduleButton.addEventListener("click", () => {
  wilsoninvestmentsModal.style.display = "flex";
  wilsoninvestmentsModal.showModal();
});

const wilsoninvestmentsModalButton = wilsoninvestmentsModal.querySelector(".button-container button");
if (wilsoninvestmentsModalButton) {
  wilsoninvestmentsModalButton.addEventListener("click", () => {
    wilsoninvestmentsModal.close();
    wilsoninvestmentsModal.style.display = "none";
  });
}

const livelabelsScheduleButton = document.querySelector(
  "#livelabels-schedule-button",
) as HTMLButtonElement;
const livelabelsModal = document.querySelector(
  "#livelabels-modal",
) as HTMLDialogElement;

livelabelsScheduleButton.addEventListener("click", () => {
  livelabelsModal.style.display = "flex";
  livelabelsModal.showModal();
});

const livelabelsModalButton = livelabelsModal.querySelector(".button-container button");
if (livelabelsModalButton) {
  livelabelsModalButton.addEventListener("click", () => {
    livelabelsModal.close();
    livelabelsModal.style.display = "none";
  });
}

const gtenterprisesScheduleButton = document.querySelector(
  "#gtenterprises-schedule-button",
) as HTMLButtonElement;
const gtenterprisesModal = document.querySelector(
  "#gtenterprises-modal",
) as HTMLDialogElement;

gtenterprisesScheduleButton.addEventListener("click", () => {
  gtenterprisesModal.style.display = "flex";
  gtenterprisesModal.showModal();
});

const gtenterprisesModalButton = gtenterprisesModal.querySelector(".button-container button");
if (gtenterprisesModalButton) {
  gtenterprisesModalButton.addEventListener("click", () => {
    gtenterprisesModal.close();
    gtenterprisesModal.style.display = "none";
  });
}

const msskScheduleButton = document.querySelector(
  "#mssk-schedule-button",
) as HTMLButtonElement;
const msskModal = document.querySelector("#mssk-modal") as HTMLDialogElement;

msskScheduleButton.addEventListener("click", () => {
  msskModal.style.display = "flex";
  msskModal.showModal();
});

const msskModalButton = msskModal.querySelector(".button-container button");
if (msskModalButton) {
  msskModalButton.addEventListener("click", () => {
    msskModal.close();
    msskModal.style.display = "none";
  });
}

const goldScheduleButton = document.querySelector(
  "#gold-schedule-button",
) as HTMLButtonElement;
const goldModal = document.querySelector("#gold-modal") as HTMLDialogElement;

goldScheduleButton.addEventListener("click", () => {
  goldModal.style.display = "flex";
  goldModal.showModal();
});

const goldModalButton = goldModal.querySelector(".button-container button");
if (goldModalButton) {
  goldModalButton.addEventListener("click", () => {
    goldModal.close();
    goldModal.style.display = "none";
  });
}
