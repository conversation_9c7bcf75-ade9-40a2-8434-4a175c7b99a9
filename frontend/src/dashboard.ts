import { Data } from "./utils.js";

fetch("/user")
  .then((response) => response.json())
  .then((data: Data) => {
    const _name = document.querySelector("#name") as HTMLParagraphElement;
    const email = document.querySelector("#email") as HTMLParagraphElement;
    const phone = document.querySelector("#phone") as HTMLParagraphElement;
    _name.innerText = data.firstName + " " + data.lastName;
    email.innerText = data.email;
    phone.innerText = data.phone;

    const navSecondaryButton = document.querySelector(
      "#nav-secondary-a"
    ) as HTMLAnchorElement;
    const navPrimaryButton = document.querySelector(
      "#nav-primary-a"
    ) as HTMLAnchorElement;

    console.log(navPrimaryButton, navSecondaryButton);

    navSecondaryButton.innerText = "Marketplace";
    navSecondaryButton.href = "/marketplace";

    navPrimaryButton.parentElement!.style.maxWidth = "fit-content";
    navPrimaryButton.innerText = "Logout";
    navPrimaryButton.href = "/logout";

    console.log(navPrimaryButton, navSecondaryButton);
  });
