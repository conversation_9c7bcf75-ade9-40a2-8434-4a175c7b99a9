const firstName = document.querySelector("#first-name-input") as HTMLInputElement;
const lastName = document.querySelector("#last-name-input") as HTMLInputElement;
const email = document.querySelector("#email-input") as HTMLInputElement;
const tel = document.querySelector("#tel-input") as HTMLInputElement;
const password = document.querySelector("#password-input") as HTMLInputElement;
const confirmPassword = document.querySelector("#confirm-password-input") as HTMLInputElement;
const errorMessage = document.querySelector(".error-message") as HTMLDivElement;


const primaryButton = document.querySelector(".primary-button") as HTMLButtonElement;
const form = document.querySelector("form") as HTMLFormElement;

const validate = (e: Event): void => {
  e.preventDefault();

  if (!validateInputs(firstName.value, lastName.value, email.value, tel.value, password.value, confirmPassword.value)) {
    return;
  }

  if (!validateName(firstName.value, lastName.value)) {
    return;
  }

  if (!validateEmail(email.value)) {
    return;
  }

  if (!validatePassword(password.value, confirmPassword.value)) {
    return;
  }

  if (!validateTel(tel.value)) {
    return;
  }

  form.submit();

};

function validateInputs(firstName: string, lastName: string, email: string, tel: string, password: string, confirmPassword: string): boolean {
  if (firstName.trim() === "" || lastName.trim() === "" || email.trim() === "" || tel.trim() === "" || password.trim() === "" || confirmPassword.trim() === "") {
    errorMessageFunction("Please fill in all fields", false);
    return false;
  }
  return true;
}

function validateName(name: string, lastName: string): boolean {
  if (name.length > 50 || lastName.length > 50) {
    errorMessageFunction("Name must be between 1 and 50 characters long", false);
    return false;
  }

  return true;
}

function validateEmail(email: string): boolean {
  //Robust email validation regex
  // const regexp = new RegExp(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/);

  //Simple email validation regex
  const regexp = new RegExp("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

  if (!regexp.test(email)) {
    errorMessageFunction("Invalid email", false);
    return false;
  }

  return true;
}

function validatePassword(password: string, confirmPassword: string): boolean {
  if (password !== confirmPassword) {
    errorMessageFunction("Passwords do not match", false);
    return false;
  }

  if (password.length < 8 || confirmPassword.length < 8) {
    errorMessageFunction("Password must be at least 8 characters long", false);
    return false;
  }

  if (password.length > 255 || confirmPassword.length > 255) {
    errorMessageFunction("Password must be less than 255 characters long", false);
    return false;
  }

  return true;
}

function validateTel(tel: string): boolean {
  const regexp = new RegExp(/(?:([+]\d{1,4})[-.\s]?)?(?:[(](\d{1,3})[)][-.\s]?)?(\d{1,4})[-.\s]?(\d{1,4})[-.\s]?(\d{1,9})/g);

  if (!regexp.test(tel)) {
    errorMessageFunction("Invalid phone number. Do not include spaces or special characters", false);
    return false;
  }

  return true;
}

function errorMessageFunction(error: string, hide: boolean): void {
  if (hide) {
    errorMessage.style.display = "none";
  } else {
    errorMessage.style.display = "block";
    errorMessage.style.color = "red";
    errorMessage.innerHTML = error;
  }
}

// throws error after submitting a successful form.
[firstName, lastName, email, tel, password, confirmPassword].forEach((input) => {
  input.addEventListener("change", () => {
    errorMessageFunction("", true);
  });
});

primaryButton.addEventListener("click", validate);
