const navSecondaryButton = document.querySelector(
  "#nav-secondary-a",
) as HTMLAnchorElement;
const navPrimaryButton = document.querySelector(
  "#nav-primary-a",
) as HTMLAnchorElement;

fetch("/user")
  .then((response) => response.json())
  .then((_data) => {
    navSecondaryButton.innerText = "Marketplace";
    navSecondaryButton.href = "/marketplace";

    if (navPrimaryButton.parentElement) {
      navPrimaryButton.parentElement.style.maxWidth = "fit-content";
    }
    navPrimaryButton.innerText = "Dashboard";
    navPrimaryButton.href = "/dashboard";
  })
  .catch(() => {});
